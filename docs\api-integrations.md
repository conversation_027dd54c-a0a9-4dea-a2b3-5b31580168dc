# 🔌 API Integrations - Multi-Agent Trading System

## Overview
Bu dokümantasyon, multi-agent trading sisteminde kullanılan tüm API entegrasyonlarını, gerekli kimlik bilgilerini ve n8n node konfigürasyonlarını içerir.

## 📊 Market Data APIs

### 1. Binance API
**Kullanım:** Gerçek zamanlı fiyat, hacim ve portföy verileri

**Gerekli Kimlik Bilgileri:**
- API Key
- Secret Key
- (Opsiyonel) Testnet için ayrı anahtarlar

**n8n Credential Configuration:**
```json
{
  "name": "Binance API",
  "type": "httpHeaderAuth",
  "data": {
    "name": "X-MBX-APIKEY",
    "value": "{{$credentials.binanceApiKey}}"
  }
}
```

**Kullanılan Endpoints:**
```javascript
// Spot Price Data
GET /api/v3/ticker/24hr?symbol=BTCUSDT

// Account Information
GET /api/v3/account

// Order Book
GET /api/v3/depth?symbol=BTCUSDT&limit=100

// Kline/Candlestick Data
GET /api/v3/klines?symbol=BTCUSDT&interval=1h&limit=100
```

**n8n HTTP Request Node Example:**
```json
{
  "method": "GET",
  "url": "https://api.binance.com/api/v3/ticker/24hr",
  "authentication": "predefinedCredentialType",
  "nodeCredentialType": "binanceApi",
  "qs": {
    "symbol": "BTCUSDT"
  },
  "headers": {
    "Content-Type": "application/json"
  }
}
```

### 2. CoinGecko API
**Kullanım:** Piyasa kapitalizasyonu, fiyat geçmişi, genel piyasa verileri

**Gerekli Kimlik Bilgileri:**
- API Key (Pro plan için)
- Free tier için kimlik bilgisi gerekmez

**Kullanılan Endpoints:**
```javascript
// Market Data
GET /api/v3/simple/price?ids=bitcoin,ethereum&vs_currencies=usd&include_24hr_change=true

// Market Cap Rankings
GET /api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100

// Historical Data
GET /api/v3/coins/bitcoin/history?date=30-12-2023

// Global Market Data
GET /api/v3/global
```

### 3. Alternative.me Fear & Greed Index
**Kullanım:** Piyasa duygu göstergesi

**Endpoint:**
```javascript
GET https://api.alternative.me/fng/?limit=1&format=json
```

**n8n HTTP Request Node:**
```json
{
  "method": "GET",
  "url": "https://api.alternative.me/fng/",
  "qs": {
    "limit": "1",
    "format": "json"
  }
}
```

## 📰 News & Social Media APIs

### 1. NewsAPI
**Kullanım:** Kripto ile ilgili haberler

**Gerekli Kimlik Bilgileri:**
- API Key

**n8n Credential Configuration:**
```json
{
  "name": "NewsAPI",
  "type": "httpHeaderAuth",
  "data": {
    "name": "X-API-Key",
    "value": "{{$credentials.newsApiKey}}"
  }
}
```

**Kullanılan Endpoints:**
```javascript
// Everything endpoint for crypto news
GET /v2/everything?q=bitcoin OR ethereum OR crypto&sortBy=publishedAt&language=en

// Top headlines
GET /v2/top-headlines?category=business&q=cryptocurrency
```

### 2. CryptoPanic API
**Kullanım:** Kripto-spesifik haberler ve sosyal medya

**Gerekli Kimlik Bilgileri:**
- API Token

**Endpoint:**
```javascript
GET https://cryptopanic.com/api/v1/posts/?auth_token=YOUR_TOKEN&public=true&kind=news
```

### 3. Twitter/X API v2
**Kullanım:** Kripto influencer'ları ve trend analizi

**Gerekli Kimlik Bilgileri:**
- Bearer Token
- API Key
- API Secret
- Access Token
- Access Token Secret

**n8n Credential Configuration:**
```json
{
  "name": "Twitter API v2",
  "type": "oAuth2Api",
  "data": {
    "clientId": "{{$credentials.twitterClientId}}",
    "clientSecret": "{{$credentials.twitterClientSecret}}",
    "accessToken": "{{$credentials.twitterAccessToken}}",
    "accessTokenSecret": "{{$credentials.twitterAccessTokenSecret}}"
  }
}
```

**Kullanılan Endpoints:**
```javascript
// Search recent tweets
GET /2/tweets/search/recent?query=bitcoin OR ethereum&max_results=100

// User timeline
GET /2/users/by/username/elonmusk/tweets
```

## 🤖 AI & Analysis APIs

### 1. OpenAI GPT-4
**Kullanım:** Duygu analizi, metin işleme, stratejik analiz

**Gerekli Kimlik Bilgileri:**
- API Key
- Organization ID (opsiyonel)

**n8n OpenAI Node Configuration:**
```json
{
  "model": "gpt-4",
  "maxTokens": 2000,
  "temperature": 0.3,
  "messages": [
    {
      "role": "system",
      "content": "Sen bir kripto piyasa analisti ve duygu analizi uzmanısın..."
    },
    {
      "role": "user",
      "content": "{{$json.newsContent}}"
    }
  ]
}
```

**Örnek Prompt Templates:**

**Sentiment Analysis Prompt:**
```
Aşağıdaki kripto haberini analiz et ve şu bilgileri JSON formatında ver:

Haber: "{{$json.title}} - {{$json.content}}"

Çıktı formatı:
{
  "sentiment_score": -1.0 ile 1.0 arası sayı,
  "sentiment_label": "very_negative|negative|neutral|positive|very_positive",
  "market_impact": "high|medium|low|negligible",
  "mentioned_coins": ["BTC", "ETH"],
  "mentioned_entities": ["SEC", "ETF"],
  "key_points": ["önemli nokta 1", "önemli nokta 2"]
}
```

**Strategic Analysis Prompt:**
```
Sen bir baş yatırım stratejistisin. Aşağıdaki verileri analiz et:

Teknik Analiz: {{$json.technicalData}}
Haber Analizi: {{$json.newsData}}
Risk Değerlendirmesi: {{$json.riskData}}

JSON formatında şunları ver:
{
  "primary_scenario": {
    "description": "Ana senaryo açıklaması",
    "probability": 0.75,
    "timeframe": "1-3 gün"
  },
  "alternative_scenario": {
    "description": "Alternatif senaryo",
    "probability": 0.25
  },
  "conflicts": ["Tespit edilen çelişkiler"],
  "recommendation": {
    "action": "BUY|SELL|HOLD|WAIT",
    "symbol": "BTCUSDT",
    "confidence": 0.85,
    "reasoning": "Detaylı gerekçe"
  }
}
```

## 💬 Communication APIs

### 1. Telegram Bot API
**Kullanım:** Kullanıcı ile iletişim

**Gerekli Kimlik Bilgileri:**
- Bot Token (BotFather'dan alınır)
- Chat ID

**n8n Telegram Node Configuration:**
```json
{
  "chatId": "{{$credentials.telegramChatId}}",
  "text": "{{$json.message}}",
  "parseMode": "Markdown",
  "replyMarkup": {
    "inlineKeyboard": [
      [
        {
          "text": "✅ Kabul Et",
          "callbackData": "accept_{{$json.insightId}}"
        },
        {
          "text": "❌ Reddet",
          "callbackData": "reject_{{$json.insightId}}"
        }
      ]
    ]
  }
}
```

## 🗄️ Database Connection

### PostgreSQL Connection
**n8n Postgres Node Configuration:**
```json
{
  "host": "localhost",
  "port": 5432,
  "database": "trading_agents",
  "user": "{{$credentials.postgresUser}}",
  "password": "{{$credentials.postgresPassword}}",
  "ssl": false
}
```

**Örnek Query Templates:**

**Insert Market Data:**
```sql
INSERT INTO market_data (symbol, price, volume_24h, price_change_percentage_24h, source)
VALUES ($1, $2, $3, $4, $5)
RETURNING id;
```

**Get Latest Technical Analysis:**
```sql
SELECT * FROM technical_analysis 
WHERE symbol = $1 
ORDER BY timestamp DESC 
LIMIT 1;
```

**Update Sentiment Analysis:**
```sql
UPDATE news_and_sentiment 
SET sentiment_score = $1, 
    sentiment_label = $2, 
    market_impact = $3,
    mentioned_coins = $4,
    is_processed = true
WHERE id = $5;
```

## 🔐 Security Best Practices

### API Key Management
1. **n8n Credentials Store:** Tüm API anahtarları n8n'in güvenli kimlik bilgisi deposunda saklanır
2. **Environment Variables:** Hassas bilgiler için ortam değişkenleri kullanılır
3. **Rate Limiting:** Her API için uygun rate limit'ler uygulanır
4. **Error Handling:** API hatalarında hassas bilgilerin loglanmaması sağlanır

### Rate Limiting Configuration
```json
{
  "binance": {
    "requests_per_minute": 1200,
    "requests_per_second": 20
  },
  "coingecko": {
    "requests_per_minute": 50
  },
  "openai": {
    "requests_per_minute": 60,
    "tokens_per_minute": 90000
  },
  "telegram": {
    "requests_per_second": 30
  }
}
```

## 🔄 Webhook Configurations

### Internal Agent Communication
```javascript
// Mastermind'ı tetiklemek için webhook
POST http://localhost:5678/webhook/trigger-mastermind
{
  "trigger_type": "high_impact_news",
  "news_id": 123,
  "impact_level": "high"
}

// Telegram Herald'ı tetiklemek için webhook
POST http://localhost:5678/webhook/send-insight
{
  "insight_id": 456,
  "urgency": "high"
}
```

### External Webhooks
```javascript
// Binance WebSocket için webhook endpoint
POST http://localhost:5678/webhook/binance-stream
{
  "stream": "btcusdt@ticker",
  "data": {
    "s": "BTCUSDT",
    "c": "43250.00",
    "P": "2.15"
  }
}
```

## 📊 Monitoring & Logging

### API Health Checks
```json
{
  "healthcheck_endpoints": [
    "https://api.binance.com/api/v3/ping",
    "https://api.coingecko.com/api/v3/ping",
    "https://api.openai.com/v1/models"
  ],
  "check_interval": "5 minutes",
  "alert_threshold": "3 consecutive failures"
}
```

### Error Handling Patterns
```javascript
// n8n Error Handling Node Configuration
{
  "continueOnFail": true,
  "retryOnFail": true,
  "maxTries": 3,
  "waitBetweenTries": 1000,
  "errorHandling": {
    "logError": true,
    "sendAlert": true,
    "fallbackAction": "use_cached_data"
  }
}
```

---

**Geliştirici:** inkbytefo  
**Dosya:** api-integrations.md  
**Son Güncelleme:** 2025-01-21
