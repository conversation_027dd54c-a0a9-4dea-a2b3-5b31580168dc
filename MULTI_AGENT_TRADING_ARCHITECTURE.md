# 🚀 Multi-Agent AI Trading Ekosistemi <PERSON>

## 📋 <PERSON><PERSON>u proje, "çılgın bir asistan" konseptini hayata geçiren, pro<PERSON><PERSON><PERSON>, işbirlikçi ve derinlemesine analiz yapabilen bir kripto trading sistemidir. Tek bir devasa workflow yerine, her biri belirli bir görevde uzmanlaşmış, birbirleriyle PostgreSQL veritabanı üzerinden haberleşen birden çok küçük ve yönetilebilir n8n workflow'undan oluşur.

## 🏛️ Temel Altyapı

### PostgreSQL Veritabanı (Merkezi Hafıza)
Tüm ajanların okuma ve yazma yaptığı, sistemin "ortak beyni" veya "karatahtası":

- **`market_data`**: Ham fiyat, hacim vb. veriler
- **`technical_analysis`**: RSI, MACD, Bollinger Bantları gibi teknik gösterge sonuçları
- **`news_and_sentiment`**: <PERSON><PERSON>, sosyal medya gönderileri ve duygu analizi skorları
- **`risk_assessments`**: Portföy risk durumu, volatilite endeksleri ve genel piyasa risk skorları
- **`synthesis_reports`**: Ana stratejist ajanın ürettiği birleştirilmiş raporlar
- **`actionable_insights`**: Son kullanıcıya gönderilmeye hazır, eyleme dönüştürülebilir tavsiyeler

### n8n Ortamı
Docker üzerinde çalışan, gerekli tüm kimlik bilgilerinin (API anahtarları) n8n'in kendi kimlik bilgisi yöneticisinde güvenli bir şekilde saklandığı yapı.

## 🤖 Ajan Workflow'ları (Uzman Çalışanlar)

### 🕵️‍♂️ Ajan 1: Veri Toplayıcı (The Data Harvester)

**Görevi:** Piyasadaki ham veriyi toplamak

**Tetikleyici:** Her 1 dakikada bir çalışan `Schedule Trigger`

**Adımları:**
1. Binance/CoinGecko API'lerinden anlık fiyat ve hacim verilerini çeker
2. NewsAPI, CryptoPanic gibi kaynaklardan en son haber başlıklarını toplar
3. Alternative.me'den Korku ve Açgözlülük Endeksi'ni alır
4. (Opsiyonel) Twitter/X API'si ile belirli anahtar kelimelerle ilgili tweet'leri çeker

**Çıktısı:** Topladığı ham verileri PostgreSQL'deki `market_data` ve `news_and_sentiment` tablolarına yazar

### 📈 Ajan 2: Teknik Analist (The Chart Whisperer)

**Görevi:** Fiyat grafiklerini ve matematiksel göstergeleri yorumlamak

**Tetikleyici:** Her 5 dakikada bir çalışan `Schedule Trigger`

**Adımları:**
1. Veritabanındaki son `market_data` kayıtlarını okur
2. `Code` nodu içinde RSI, MACD, Ichimoku Bulutları, Bollinger Bantları gibi popüler göstergeleri hesaplar
3. Önemli formasyonları (örneğin, OBO, TOBO, bayrak) tespit etmeye yönelik basit algoritmalar çalıştırır

**Çıktısı:** Hesapladığı gösterge değerlerini ve tespit ettiği formasyonları `technical_analysis` tablosuna yazar

### 📰 Ajan 3: Duygu ve Haber Analisti (The Sentiment Scryer)

**Görevi:** Metin tabanlı verilerin piyasa üzerindeki potansiyel etkisini ölçmek

**Tetikleyici:** `news_and_sentiment` tablosuna yeni bir kayıt eklendiğinde tetiklenir

**Adımları:**
1. İşlenmemiş haber ve sosyal medya metinlerini veritabanından okur
2. **OpenAI (GPT-4) nodunu kullanarak:**
   - Metnin duygu skorunu (pozitif, negatif, nötr) belirler
   - Metnin potansiyel piyasa etkisini (yüksek, orta, düşük) tahmin eder
   - Metindeki anahtar varlıkları (örn: "SEC", "ETF", "Ripple") çıkarır

**Çıktısı:** Analiz sonuçlarını `news_and_sentiment` tablosundaki ilgili kayıtları güncelleyerek yazar

### 🛡️ Ajan 4: Risk Yöneticisi (The Guardian)

**Görevi:** Portföyü ve piyasayı risk açısından sürekli denetlemek

**Tetikleyici:** Her 10 dakikada bir çalışan `Schedule Trigger`

**Adımları:**
1. Binance API'si ile mevcut portföy durumunu ve açık pozisyonları okur
2. Piyasa volatilitesini (örn: ATR göstergesi) hesaplar
3. Günlük zarar limiti gibi önceden tanımlanmış kuralları kontrol eder

**Çıktısı:** Mevcut risk seviyesini (`DÜŞÜK`, `ORTA`, `YÜKSEK`, `TEHLİKE`) ve portföy sağlık durumunu `risk_assessments` tablosuna yazar

## 🧠 Ana Workflow: Stratejist Beyin (The Mastermind)

**Görevi:** Ajanlardan gelen verileri sentezlemek, çelişkileri bulmak, gizli pattern'leri tespit etmek ve eyleme dönüştürülebilir bir tavsiye oluşturmak

**Tetikleyici:** Her 15 dakikada bir veya yüksek etkili bir haber geldiğinde Ajan 3 tarafından bir webhook ile tetiklenir

**Adımları:**
1. **İstihbarat Toplama:** Tüm ajanların en son raporlarını veritabanından tek bir sorgu ile çeker
2. **Sentezleme (En Kritik Adım):** Toplanan tüm veriyi yapılandırılmış bir metin haline getirir ve bunu **OpenAI (GPT-4) noduna** bir "master prompt" ile gönderir
3. **Karar Verme:** AI'dan gelen yapılandırılmış JSON yanıtını alır

### Örnek Master Prompt:
```
Sen, farklı uzmanlardan (teknik, duygu, risk) raporlar alan bir baş yatırım stratejistisin. 
İşte son raporlar: [Teknik Analiz Verileri], [Haber ve Duygu Analizi Verileri], [Risk Değerlendirmesi]. 

Bu verileri sentezle. Teknik göstergelerle haberler arasında bir çelişki var mı? 
Geçmişte benzer bir veri kümesi hangi sonuca yol açtı? En olası piyasa senaryosunu, 
alternatif bir senaryoyu ve her biri için bir güven skoru belirle. 

Son olarak, net, gerekçeli ve eyleme dönüştürülebilir bir yatırım tavsiyesi oluştur. 
Cevabını JSON formatında ver.
```

**Çıktısı:**
- Detaylı analizi `synthesis_reports` tablosuna kaydeder (denetim için)
- Eğer güven skoru yüksekse, net tavsiyeyi `actionable_insights` tablosuna yazar
- **Telegram Asistanı workflow'unu bir webhook ile tetikler**

## 💬 Arayüz Workflow: Telegram Asistanı (The Herald)

**Görevi:** "Çılgın asistanın" kullanıcıyla konuştuğu arayüz

**Tetikleyiciler:**
1. **Proaktif:** `Mastermind` workflow'u tarafından bir `Webhook Trigger` ile tetiklenir
2. **Reaktif:** Kullanıcının komutları için standart `Telegram Trigger`

**Adımları:**
1. **Proaktif Akış:** `actionable_insights` tablosundan en son tavsiyeyi okur
2. **Reaktif Akış:** Kullanıcının `/status`, `/analiz_detay` gibi komutlarına göre ilgili tablolardan veri çeker
3. Veriyi, kullanıcı dostu ve ilgi çekici bir mesaja dönüştürür

### Örnek Proaktif Mesaj:
```
🔥 **ÇILGIN FIRSAT TESPİT EDİLDİ!** 🔥

**Ajanlarım Fısıldıyor:** Teknik analiz (RSI < 30) ve haber akışı (ETF onayı söylentisi) 
aynı anda **GÜÇLÜ AL** sinyali veriyor! Risk ajanı piyasa volatilitesinin yönetilebilir 
olduğunu bildirdi.

**Tavsiye: XRP - AL**
🎯 **Güven Skoru:** %85
📈 **Potansiyel Getiri:** 3-5 gün içinde %15-%25
🛡️ **Gerekçe:** Teknik olarak aşırı satım bölgesinde ve pozitif haber akışıyla destekleniyor. 
Bu pattern, geçen ayki yükselişten önce de görülmüştü.

Ne yapalım Kaptan?
[✅ 500 TRY AL] [📊 Detaylı Raporu Göster] [😴 Şimdilik Kalsın]
```

**Çıktısı:** Kullanıcıya Telegram üzerinden mesaj gönderir ve butonlarla etkileşim bekler

## 🎯 Mimarinin Avantajları

### Modülerlik
Her ajan bağımsızdır. Bir ajanı (örneğin, teknik analisti) daha gelişmiş bir versiyonuyla değiştirmek, tüm sistemi bozmadan yapılabilir.

### Ölçeklenebilirlik
Yeni ajanlar (örn: "Ekonomik Takvim Ajanı", "Balina Hareketleri Ajanı") ekosisteme kolayca eklenebilir.

### Dayanıklılık
Bir ajan hata verirse, diğerleri çalışmaya devam eder. Sistem tamamen çökmez.

### Yönetilebilirlik
Her bir workflow küçük ve odaklı olduğu için hata ayıklaması ve geliştirilmesi çok daha kolaydır.

## 📁 Proje Yapısı

```
n8n-factory/
├── docs/
│   ├── MULTI_AGENT_TRADING_ARCHITECTURE.md (bu dosya)
│   ├── database-schema.md
│   └── api-integrations.md
├── workflows/
│   ├── agents/
│   │   ├── data-harvester.json
│   │   ├── technical-analyst.json
│   │   ├── sentiment-analyst.json
│   │   └── risk-manager.json
│   ├── mastermind.json
│   └── telegram-herald.json
├── database/
│   ├── schema.sql
│   └── seed-data.sql
└── docker/
    ├── docker-compose.yml
    └── n8n/
        └── Dockerfile
```

## 🚀 Sonraki Adımlar

1. **Veritabanı Şeması Tasarımı**: PostgreSQL tablolarının detaylı yapısını oluştur
2. **API Entegrasyonları**: Gerekli API anahtarları ve endpoint'lerin dökümantasyonu
3. **Workflow Geliştirme**: Her bir ajan için n8n workflow'larının oluşturulması
4. **Test ve Optimizasyon**: Sistem performansı ve güvenilirlik testleri
5. **Deployment**: Docker container'ları ile production ortamına geçiş

---

**Geliştirici:** inkbytefo  
**Proje Adı:** n8n Multi-Agent Trading Factory  
**Versiyon:** 1.0.0  
**Son Güncelleme:** 2025-01-21
