# 🗄️ Database Schema - Multi-Agent Trading System

## Overview
PostgreSQL veritabanı şeması, tüm ajan<PERSON>ın ortak hafızası olarak görev yapar. Her tablo, belirli bir veri tür<PERSON>n<PERSON> saklar ve ajanlar arası iletişimi <PERSON>.

## 📊 Core Tables

### 1. market_data
Ham piyasa verilerini saklar (Veri Toplayıcı Ajan tarafından doldurulur)

```sql
CREATE TABLE market_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    volume_24h DECIMAL(20, 8),
    market_cap DECIMAL(20, 2),
    price_change_24h DECIMAL(10, 4),
    price_change_percentage_24h DECIMAL(10, 4),
    fear_greed_index INTEGER,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(50) NOT NULL, -- 'binance', 'coingecko', etc.
    
    INDEX idx_symbol_timestamp (symbol, timestamp),
    INDEX idx_timestamp (timestamp)
);
```

### 2. technical_analysis
Teknik gösterge sonuçlarını saklar (Teknik Analist Ajan tarafından doldurulur)

```sql
CREATE TABLE technical_analysis (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL, -- '1m', '5m', '1h', '1d'
    
    -- RSI Indicators
    rsi_14 DECIMAL(5, 2),
    rsi_signal VARCHAR(10), -- 'oversold', 'overbought', 'neutral'
    
    -- MACD Indicators
    macd_line DECIMAL(10, 6),
    macd_signal DECIMAL(10, 6),
    macd_histogram DECIMAL(10, 6),
    macd_trend VARCHAR(10), -- 'bullish', 'bearish', 'neutral'
    
    -- Bollinger Bands
    bb_upper DECIMAL(20, 8),
    bb_middle DECIMAL(20, 8),
    bb_lower DECIMAL(20, 8),
    bb_position VARCHAR(10), -- 'above', 'below', 'middle'
    
    -- Moving Averages
    sma_20 DECIMAL(20, 8),
    sma_50 DECIMAL(20, 8),
    ema_12 DECIMAL(20, 8),
    ema_26 DECIMAL(20, 8),
    
    -- Pattern Recognition
    detected_patterns JSONB, -- Array of detected patterns
    support_level DECIMAL(20, 8),
    resistance_level DECIMAL(20, 8),
    
    -- Overall Signal
    overall_signal VARCHAR(20), -- 'strong_buy', 'buy', 'hold', 'sell', 'strong_sell'
    confidence_score DECIMAL(3, 2), -- 0.00 to 1.00
    
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_symbol_timeframe_timestamp (symbol, timeframe, timestamp),
    INDEX idx_overall_signal (overall_signal)
);
```

### 3. news_and_sentiment
Haber ve sosyal medya verilerini saklar (Veri Toplayıcı ve Duygu Analisti ajanları tarafından kullanılır)

```sql
CREATE TABLE news_and_sentiment (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT,
    url VARCHAR(500),
    source VARCHAR(100) NOT NULL, -- 'newsapi', 'cryptopanic', 'twitter'
    author VARCHAR(200),
    
    -- Raw Data
    published_at TIMESTAMP WITH TIME ZONE,
    collected_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Sentiment Analysis (filled by Sentiment Analyst)
    sentiment_score DECIMAL(3, 2), -- -1.00 to 1.00 (negative to positive)
    sentiment_label VARCHAR(20), -- 'very_negative', 'negative', 'neutral', 'positive', 'very_positive'
    market_impact VARCHAR(20), -- 'high', 'medium', 'low', 'negligible'
    
    -- Entity Extraction
    mentioned_coins JSONB, -- Array of mentioned cryptocurrencies
    mentioned_entities JSONB, -- Array of important entities (SEC, ETF, etc.)
    
    -- Processing Status
    is_processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,
    
    INDEX idx_published_at (published_at),
    INDEX idx_sentiment_score (sentiment_score),
    INDEX idx_market_impact (market_impact),
    INDEX idx_is_processed (is_processed)
);
```

### 4. risk_assessments
Risk değerlendirmelerini saklar (Risk Yöneticisi Ajan tarafından doldurulur)

```sql
CREATE TABLE risk_assessments (
    id SERIAL PRIMARY KEY,
    
    -- Portfolio Data
    total_portfolio_value DECIMAL(20, 2),
    total_pnl_24h DECIMAL(20, 2),
    total_pnl_percentage_24h DECIMAL(10, 4),
    
    -- Risk Metrics
    portfolio_volatility DECIMAL(10, 6), -- ATR-based volatility
    max_drawdown DECIMAL(10, 4),
    sharpe_ratio DECIMAL(10, 6),
    
    -- Position Analysis
    open_positions JSONB, -- Array of current positions
    largest_position_percentage DECIMAL(5, 2),
    
    -- Risk Levels
    overall_risk_level VARCHAR(20), -- 'LOW', 'MEDIUM', 'HIGH', 'DANGER'
    market_volatility_level VARCHAR(20),
    liquidity_risk VARCHAR(20),
    
    -- Risk Limits
    daily_loss_limit DECIMAL(20, 2),
    current_daily_loss DECIMAL(20, 2),
    is_daily_limit_exceeded BOOLEAN DEFAULT FALSE,
    
    -- Recommendations
    risk_recommendations JSONB, -- Array of risk management suggestions
    
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_timestamp (timestamp),
    INDEX idx_overall_risk_level (overall_risk_level),
    INDEX idx_daily_limit_exceeded (is_daily_limit_exceeded)
);
```

### 5. synthesis_reports
Ana stratejist ajanın birleştirilmiş raporlarını saklar (Mastermind tarafından doldurulur)

```sql
CREATE TABLE synthesis_reports (
    id SERIAL PRIMARY KEY,
    
    -- Input Data References
    technical_analysis_ids JSONB, -- Array of technical_analysis IDs used
    news_sentiment_ids JSONB, -- Array of news_and_sentiment IDs used
    risk_assessment_id INTEGER REFERENCES risk_assessments(id),
    
    -- AI Analysis
    ai_prompt TEXT NOT NULL,
    ai_response JSONB NOT NULL, -- Full structured AI response
    
    -- Extracted Insights
    market_scenario_primary JSONB, -- Primary scenario with confidence
    market_scenario_alternative JSONB, -- Alternative scenario
    detected_conflicts JSONB, -- Conflicts between different data sources
    historical_patterns JSONB, -- Similar historical patterns found
    
    -- Decision Making
    overall_confidence DECIMAL(3, 2), -- 0.00 to 1.00
    recommendation_type VARCHAR(20), -- 'BUY', 'SELL', 'HOLD', 'WAIT'
    recommended_symbols JSONB, -- Array of symbols with actions
    
    -- Metadata
    processing_time_ms INTEGER,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_timestamp (timestamp),
    INDEX idx_recommendation_type (recommendation_type),
    INDEX idx_overall_confidence (overall_confidence)
);
```

### 6. actionable_insights
Kullanıcıya gönderilmeye hazır tavsiyeler (Mastermind tarafından doldurulur, Telegram Herald tarafından okunur)

```sql
CREATE TABLE actionable_insights (
    id SERIAL PRIMARY KEY,
    synthesis_report_id INTEGER REFERENCES synthesis_reports(id),
    
    -- Core Recommendation
    action_type VARCHAR(20) NOT NULL, -- 'BUY', 'SELL', 'HOLD', 'ALERT'
    symbol VARCHAR(20) NOT NULL,
    confidence_score DECIMAL(3, 2) NOT NULL,
    
    -- Financial Details
    suggested_amount DECIMAL(20, 2),
    suggested_percentage DECIMAL(5, 2), -- Percentage of portfolio
    target_price DECIMAL(20, 8),
    stop_loss DECIMAL(20, 8),
    take_profit DECIMAL(20, 8),
    
    -- Reasoning
    primary_reason TEXT NOT NULL,
    supporting_factors JSONB, -- Array of supporting factors
    risk_factors JSONB, -- Array of risk factors
    
    -- Timing
    urgency_level VARCHAR(20), -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    expected_timeframe VARCHAR(50), -- '1-3 days', '1 week', etc.
    
    -- User Interaction
    is_sent_to_user BOOLEAN DEFAULT FALSE,
    user_response VARCHAR(20), -- 'ACCEPTED', 'REJECTED', 'PENDING'
    user_response_timestamp TIMESTAMP WITH TIME ZONE,
    
    -- Execution Tracking
    is_executed BOOLEAN DEFAULT FALSE,
    execution_price DECIMAL(20, 8),
    execution_timestamp TIMESTAMP WITH TIME ZONE,
    
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_timestamp (timestamp),
    INDEX idx_action_type (action_type),
    INDEX idx_is_sent_to_user (is_sent_to_user),
    INDEX idx_urgency_level (urgency_level)
);
```

## 🔄 Agent Communication Patterns

### Data Flow
1. **Data Harvester** → `market_data`, `news_and_sentiment`
2. **Technical Analyst** → `technical_analysis` (reads from `market_data`)
3. **Sentiment Analyst** → updates `news_and_sentiment` (reads unprocessed records)
4. **Risk Manager** → `risk_assessments`
5. **Mastermind** → `synthesis_reports`, `actionable_insights` (reads from all tables)
6. **Telegram Herald** → reads from `actionable_insights`

### Trigger Mechanisms
```sql
-- Trigger to notify Sentiment Analyst when new news arrives
CREATE OR REPLACE FUNCTION notify_new_news()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM pg_notify('new_news_channel', NEW.id::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER new_news_trigger
    AFTER INSERT ON news_and_sentiment
    FOR EACH ROW
    EXECUTE FUNCTION notify_new_news();

-- Trigger to notify Mastermind when high-impact news is processed
CREATE OR REPLACE FUNCTION notify_high_impact_news()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.market_impact = 'high' AND OLD.is_processed = FALSE AND NEW.is_processed = TRUE THEN
        PERFORM pg_notify('high_impact_news_channel', NEW.id::text);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER high_impact_news_trigger
    AFTER UPDATE ON news_and_sentiment
    FOR EACH ROW
    EXECUTE FUNCTION notify_high_impact_news();
```

## 🛠️ Utility Views

### Latest Market Summary
```sql
CREATE VIEW latest_market_summary AS
SELECT 
    symbol,
    price,
    price_change_percentage_24h,
    volume_24h,
    fear_greed_index,
    timestamp
FROM market_data m1
WHERE timestamp = (
    SELECT MAX(timestamp) 
    FROM market_data m2 
    WHERE m2.symbol = m1.symbol
)
ORDER BY symbol;
```

### Current Risk Status
```sql
CREATE VIEW current_risk_status AS
SELECT 
    overall_risk_level,
    portfolio_volatility,
    is_daily_limit_exceeded,
    current_daily_loss,
    daily_loss_limit,
    timestamp
FROM risk_assessments
WHERE timestamp = (SELECT MAX(timestamp) FROM risk_assessments);
```

### Pending User Actions
```sql
CREATE VIEW pending_user_actions AS
SELECT 
    id,
    action_type,
    symbol,
    confidence_score,
    primary_reason,
    urgency_level,
    timestamp
FROM actionable_insights
WHERE is_sent_to_user = TRUE 
  AND user_response = 'PENDING'
  AND timestamp > NOW() - INTERVAL '24 hours'
ORDER BY urgency_level DESC, timestamp DESC;
```

## 📈 Performance Optimization

### Partitioning Strategy
```sql
-- Partition market_data by date for better performance
CREATE TABLE market_data_y2025m01 PARTITION OF market_data
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- Create monthly partitions as needed
```

### Cleanup Procedures
```sql
-- Clean old data (keep last 30 days for most tables)
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    DELETE FROM market_data WHERE timestamp < NOW() - INTERVAL '30 days';
    DELETE FROM technical_analysis WHERE timestamp < NOW() - INTERVAL '30 days';
    DELETE FROM news_and_sentiment WHERE collected_at < NOW() - INTERVAL '7 days';
    DELETE FROM synthesis_reports WHERE timestamp < NOW() - INTERVAL '30 days';
    DELETE FROM actionable_insights WHERE timestamp < NOW() - INTERVAL '30 days' AND is_executed = TRUE;
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup to run daily
SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');
```

---

**Geliştirici:** inkbytefo  
**Dosya:** database-schema.md  
**Son Güncelleme:** 2025-01-21
