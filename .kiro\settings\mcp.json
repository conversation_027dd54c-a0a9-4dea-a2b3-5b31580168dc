{"mcpServers": {"n8n-mcp": {"command": "npx", "args": ["n8n-mcp"], "env": {"MCP_MODE": "stdio", "LOG_LEVEL": "error", "DISABLE_CONSOLE_OUTPUT": "true", "N8N_API_URL": "http://localhost:5678/", "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.6WVkeDwHhxU3vuG4ahesZccPq9teQYpOB_HfXSwV8lY"}, "disabled": false, "autoApprove": ["n8n_health_check", "n8n_list_workflows", "search_nodes", "n8n_create_workflow"]}}}